import React from 'react';
import { Fish } from 'lucide-react';
import { useCalculator } from '../hooks/useCalculator';
import { CalculatorForm } from './CalculatorForm';
import { CostBreakdown } from './CostBreakdown';

export function Calculator() {
  const {
    species,
    productForms,
    shippingRates,
    packagingMaterials,
    selectedValues,
    costs,
    handleInputChange,
  } = useCalculator();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Fish className="w-8 h-8 text-blue-600" />
          Seafood Pricing Calculator
        </h1>
        
        <CalculatorForm
          species={species}
          productForms={productForms}
          shippingRates={shippingRates}
          packagingMaterials={packagingMaterials}
          selectedValues={selectedValues}
          onChange={handleInputChange}
        />
      </div>
      
      {costs && <CostBreakdown costs={costs} />}
    </div>
  );
}
import React from 'react';
import type { Species, ProductForm, ShippingRate, PackagingMaterial } from '../types/database';

interface CalculatorFormProps {
  species: Species[];
  productForms: ProductForm[];
  shippingRates: ShippingRate[];
  packagingMaterials: PackagingMaterial[];
  selectedValues: {
    species: string;
    form: string;
    weight: number;
    shipping: string;
    packaging: string;
    region: string;
  };
  onChange: (field: string, value: string | number) => void;
}

export function CalculatorForm({
  species,
  productForms,
  shippingRates,
  packagingMaterials,
  selectedValues,
  onChange,
}: CalculatorFormProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Species</label>
          <select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.species}
            onChange={(e) => onChange('species', e.target.value)}
          >
            <option value="">Select Species</option>
            {species.map((s) => (
              <option key={s.id} value={s.id}>{s.name}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Product Form</label>
          <select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.form}
            onChange={(e) => onChange('form', e.target.value)}
          >
            <option value="">Select Form</option>
            {productForms
              .filter(f => !selectedValues.species || f.species_id === selectedValues.species)
              .map((f) => (
                <option key={f.id} value={f.id}>{f.name}</option>
              ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Input Weight (lbs)</label>
          <input
            type="number"
            min="0"
            step="0.1"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.weight || ''}
            onChange={(e) => onChange('weight', Number(e.target.value))}
          />
        </div>
      </div>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Region</label>
          <select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.region}
            onChange={(e) => onChange('region', e.target.value)}
          >
            <option value="">Select Region</option>
            <option value="northeast">Northeast</option>
            <option value="southeast">Southeast</option>
            <option value="midwest">Midwest</option>
            <option value="west">West</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Shipping Method</label>
          <select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.shipping}
            onChange={(e) => onChange('shipping', e.target.value)}
          >
            <option value="">Select Shipping</option>
            {shippingRates
              .filter(s => !selectedValues.region || s.region === selectedValues.region)
              .map((s) => (
                <option key={s.id} value={s.id}>{s.method}</option>
              ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Packaging</label>
          <select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedValues.packaging}
            onChange={(e) => onChange('packaging', e.target.value)}
          >
            <option value="">Select Packaging</option>
            {packagingMaterials.map((p) => (
              <option key={p.id} value={p.id}>{p.name}</option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
import React, { RefObject } from 'react';
import { Package, Download } from 'lucide-react';

interface ActionButtonsProps {
  onExportPDF: () => void;
  pdfRef: RefObject<HTMLDivElement>;
}

export function ActionButtons({ onExportPDF }: ActionButtonsProps) {
  return (
    <div className="mt-6 flex justify-end space-x-4">
      <button
        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        onClick={() => {/* TODO: Save quote */}}
      >
        <Package className="mr-2 h-4 w-4" />
        Save Quote
      </button>
      <button
        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        onClick={onExportPDF}
      >
        <Download className="mr-2 h-4 w-4" />
        Export PDF
      </button>
    </div>
  );
}
import React from 'react';

interface CostBreakdownProps {
  costs: {
    rawMaterialCost: number;
    processingCost: number;
    packagingCost: number;
    shippingCost: number;
    finalWeight: number;
    pricePerPound: number;
    total: number;
  };
}

export function CostBreakdown({ costs }: CostBreakdownProps) {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
      <h2 className="text-xl font-semibold mb-4">Cost Breakdown</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Raw Material Cost:</span>
            <span className="font-medium">${costs.rawMaterialCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Processing Cost:</span>
            <span className="font-medium">${costs.processingCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Packaging Cost:</span>
            <span className="font-medium">${costs.packagingCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Shipping Cost:</span>
            <span className="font-medium">${costs.shippingCost.toFixed(2)}</span>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Final Weight:</span>
            <span className="font-medium">{costs.finalWeight.toFixed(2)} lbs</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Price per Pound:</span>
            <span className="font-medium">${costs.pricePerPound.toFixed(2)}/lb</span>
          </div>
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Total Price:</span>
            <span>${costs.total.toFixed(2)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
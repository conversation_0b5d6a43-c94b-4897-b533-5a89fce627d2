import { useState, useEffect, useMemo } from 'react';
import { supabase } from '../lib/supabase';
import { calculateCosts } from '../utils/calculations';
import type { Species, ProductForm, ShippingRate, PackagingMaterial } from '../types/database';

export function useCalculator() {
  const [species, setSpecies] = useState<Species[]>([]);
  const [productForms, setProductForms] = useState<ProductForm[]>([]);
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const [packagingMaterials, setPackagingMaterials] = useState<PackagingMaterial[]>([]);
  
  const [selectedValues, setSelectedValues] = useState({
    species: '',
    form: '',
    weight: 0,
    shipping: '',
    packaging: '',
    region: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  async function loadData() {
    try {
      const [
        { data: speciesData },
        { data: formsData },
        { data: shippingData },
        { data: packagingData }
      ] = await Promise.all([
        supabase.from('species').select('*'),
        supabase.from('product_forms').select('*'),
        supabase.from('shipping_rates').select('*'),
        supabase.from('packaging_materials').select('*')
      ]);
      
      setSpecies(speciesData || []);
      setProductForms(formsData || []);
      setShippingRates(shippingData || []);
      setPackagingMaterials(packagingData || []);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  function handleInputChange(field: string, value: string | number) {
    setSelectedValues(prev => ({ ...prev, [field]: value }));
  }

  const costs = useMemo(() => {
    if (!selectedValues.species || !selectedValues.form || !selectedValues.shipping || !selectedValues.packaging || !selectedValues.weight) {
      return null;
    }
    return calculateCosts({
      selectedValues,
      species,
      productForms,
      shippingRates,
      packagingMaterials,
    });
  }, [selectedValues, species, productForms, shippingRates, packagingMaterials]);

  return {
    species,
    productForms,
    shippingRates,
    packagingMaterials,
    selectedValues,
    costs,
    handleInputChange,
  };
}
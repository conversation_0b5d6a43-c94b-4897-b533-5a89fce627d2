import { useState, useEffect, useMemo } from 'react';
import { calculateCosts } from '../utils/calculations';
import type { Species, ProductForm, ShippingRate, PackagingMaterial } from '../types/database';

// Mock data for UI preview
const mockSpecies: Species[] = [
  {
    id: '1',
    name: 'Atlantic Salmon',
    recovery_rate: 0.85,
    base_price_per_pound: 12.50,
    seasonal_adjustment: 1.0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Pacific Cod',
    recovery_rate: 0.75,
    base_price_per_pound: 8.25,
    seasonal_adjustment: 1.1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'Dungeness Crab',
    recovery_rate: 0.25,
    base_price_per_pound: 18.00,
    seasonal_adjustment: 1.2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '4',
    name: 'Pacific Halibut',
    recovery_rate: 0.80,
    base_price_per_pound: 15.75,
    seasonal_adjustment: 0.95,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const mockProductForms: ProductForm[] = [
  {
    id: '1',
    species_id: '1',
    name: 'Whole Fish',
    processing_yield: 1.0,
    processing_cost_per_pound: 0.50,
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    species_id: '1',
    name: 'Fillets',
    processing_yield: 0.65,
    processing_cost_per_pound: 2.25,
    created_at: new Date().toISOString(),
  },
  {
    id: '3',
    species_id: '2',
    name: 'Whole Fish',
    processing_yield: 1.0,
    processing_cost_per_pound: 0.40,
    created_at: new Date().toISOString(),
  },
  {
    id: '4',
    species_id: '2',
    name: 'Fillets',
    processing_yield: 0.70,
    processing_cost_per_pound: 1.85,
    created_at: new Date().toISOString(),
  },
  {
    id: '5',
    species_id: '3',
    name: 'Whole Crab',
    processing_yield: 1.0,
    processing_cost_per_pound: 1.00,
    created_at: new Date().toISOString(),
  },
  {
    id: '6',
    species_id: '3',
    name: 'Crab Meat',
    processing_yield: 0.30,
    processing_cost_per_pound: 8.50,
    created_at: new Date().toISOString(),
  },
  {
    id: '7',
    species_id: '4',
    name: 'Steaks',
    processing_yield: 0.75,
    processing_cost_per_pound: 1.75,
    created_at: new Date().toISOString(),
  },
];

const mockShippingRates: ShippingRate[] = [
  {
    id: '1',
    method: 'Ground',
    region: 'West Coast',
    base_rate: 15.00,
    rate_per_pound: 0.85,
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    method: 'Overnight',
    region: 'West Coast',
    base_rate: 45.00,
    rate_per_pound: 2.25,
    created_at: new Date().toISOString(),
  },
  {
    id: '3',
    method: 'Ground',
    region: 'East Coast',
    base_rate: 25.00,
    rate_per_pound: 1.15,
    created_at: new Date().toISOString(),
  },
  {
    id: '4',
    method: 'Overnight',
    region: 'East Coast',
    base_rate: 65.00,
    rate_per_pound: 3.50,
    created_at: new Date().toISOString(),
  },
];

const mockPackagingMaterials: PackagingMaterial[] = [
  {
    id: '1',
    name: 'Insulated Box (10 lbs)',
    cost_per_unit: 8.50,
    weight_capacity: 10,
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Insulated Box (25 lbs)',
    cost_per_unit: 15.75,
    weight_capacity: 25,
    created_at: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'Vacuum Sealed Bag',
    cost_per_unit: 2.25,
    weight_capacity: 5,
    created_at: new Date().toISOString(),
  },
  {
    id: '4',
    name: 'Styrofoam Cooler (50 lbs)',
    cost_per_unit: 25.00,
    weight_capacity: 50,
    created_at: new Date().toISOString(),
  },
];

export function useCalculator() {
  const [species, setSpecies] = useState<Species[]>([]);
  const [productForms, setProductForms] = useState<ProductForm[]>([]);
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const [packagingMaterials, setPackagingMaterials] = useState<PackagingMaterial[]>([]);

  const [selectedValues, setSelectedValues] = useState({
    species: '',
    form: '',
    weight: 0,
    shipping: '',
    packaging: '',
    region: '',
  });

  useEffect(() => {
    // Load mock data instead of from Supabase
    loadMockData();
  }, []);

  function loadMockData() {
    // Simulate loading delay
    setTimeout(() => {
      setSpecies(mockSpecies);
      setProductForms(mockProductForms);
      setShippingRates(mockShippingRates);
      setPackagingMaterials(mockPackagingMaterials);
    }, 100);
  }

  function handleInputChange(field: string, value: string | number) {
    setSelectedValues(prev => ({ ...prev, [field]: value }));
  }

  const costs = useMemo(() => {
    if (!selectedValues.species || !selectedValues.form || !selectedValues.shipping || !selectedValues.packaging || !selectedValues.weight) {
      return null;
    }
    return calculateCosts({
      selectedValues,
      species,
      productForms,
      shippingRates,
      packagingMaterials,
    });
  }, [selectedValues, species, productForms, shippingRates, packagingMaterials]);

  return {
    species,
    productForms,
    shippingRates,
    packagingMaterials,
    selectedValues,
    costs,
    handleInputChange,
  };
}
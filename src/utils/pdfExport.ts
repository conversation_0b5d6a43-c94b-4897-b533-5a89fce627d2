import type { RefObject } from 'react';

export async function exportToPDF(ref: RefObject<HTMLDivElement>) {
  if (!ref.current) return;
  
  const options = {
    filename: `seafood-quote-${new Date().toISOString().split('T')[0]}.pdf`,
    page: { margin: 20 }
  };

  try {
    const { toPDF } = await import('react-to-pdf');
    await toPDF(ref, options);
  } catch (error) {
    console.error('Error generating PDF:', error);
  }
}
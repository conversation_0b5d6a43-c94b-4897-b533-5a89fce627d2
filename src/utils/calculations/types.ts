import type { Species, ProductForm, ShippingRate, PackagingMaterial } from '../../types/database';

export interface CalculationInput {
  selectedValues: {
    species: string;
    form: string;
    weight: number;
    shipping: string;
    packaging: string;
    region: string;
  };
  species: Species[];
  productForms: ProductForm[];
  shippingRates: ShippingRate[];
  packagingMaterials: PackagingMaterial[];
}

export interface CostBreakdown {
  rawMaterialCost: number;
  processingCost: number;
  packagingCost: number;
  shippingCost: number;
  subtotal: number;
  total: number;
  pricePerPound: number;
  finalWeight: number;
}
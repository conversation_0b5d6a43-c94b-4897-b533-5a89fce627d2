import type { CalculationInput, CostBreakdown } from './types';

export function calculateCosts({
  selectedValues,
  species,
  productForms,
  shippingRates,
  packagingMaterials,
}: CalculationInput): CostBreakdown | null {
  const selectedSpeciesData = species.find(s => s.id === selectedValues.species);
  const selectedFormData = productForms.find(f => f.id === selectedValues.form);
  const selectedShippingData = shippingRates.find(s => s.id === selectedValues.shipping);
  const selectedPackagingData = packagingMaterials.find(p => p.id === selectedValues.packaging);
  
  if (!selectedSpeciesData || !selectedFormData || !selectedShippingData || !selectedPackagingData || !selectedValues.weight) {
    return null;
  }
  
  const recoveryWeight = selectedValues.weight * selectedSpeciesData.recovery_rate;
  const finalWeight = recoveryWeight * selectedFormData.processing_yield;
  
  const rawMaterialCost = selectedValues.weight * selectedSpeciesData.base_price_per_pound * selectedSpeciesData.seasonal_adjustment;
  const processingCost = finalWeight * selectedFormData.processing_cost_per_pound;
  const packagingCost = Math.ceil(finalWeight / selectedPackagingData.weight_capacity) * selectedPackagingData.cost_per_unit;
  const shippingCost = selectedShippingData.base_rate + (finalWeight * selectedShippingData.rate_per_pound);
  
  const subtotal = rawMaterialCost + processingCost + packagingCost;
  const markup = subtotal * 0.3; // 30% markup
  const total = subtotal + markup + shippingCost;
  const pricePerPound = total / finalWeight;
  
  return {
    rawMaterialCost,
    processingCost,
    packagingCost,
    shippingCost,
    subtotal,
    total,
    pricePerPound,
    finalWeight
  };
}
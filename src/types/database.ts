export interface Species {
  id: string;
  name: string;
  recovery_rate: number;
  base_price_per_pound: number;
  seasonal_adjustment: number;
  created_at: string;
  updated_at: string;
}

export interface ProductForm {
  id: string;
  species_id: string;
  name: string;
  processing_yield: number;
  processing_cost_per_pound: number;
  created_at: string;
}

export interface ShippingRate {
  id: string;
  method: string;
  region: string;
  base_rate: number;
  rate_per_pound: number;
  created_at: string;
}

export interface PackagingMaterial {
  id: string;
  name: string;
  cost_per_unit: number;
  weight_capacity: number;
  created_at: string;
}

export interface Quote {
  id: string;
  user_id: string;
  customer_name: string;
  shipping_address: string;
  shipping_method: string;
  packaging_id: string;
  subtotal: number;
  shipping_cost: number;
  total: number;
  payment_terms: string;
  delivery_timeframe: string;
  created_at: string;
  updated_at: string;
}

export interface QuoteItem {
  id: string;
  quote_id: string;
  species_id: string;
  product_form_id: string;
  input_weight: number;
  final_weight: number;
  price_per_pound: number;
  total_price: number;
  created_at: string;
}
/*
  # Seafood Pricing Database Schema

  1. New Tables
    - `species`
      - Basic species information and recovery rates
    - `product_forms`
      - Available product forms per species
    - `shipping_rates`
      - Shipping costs by method and region
    - `packaging_materials`
      - Available packaging options and costs
    - `price_history`
      - Historical price tracking
    - `quotes`
      - Saved customer quotes
    - `quote_items`
      - Individual items in quotes

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Species table
CREATE TABLE species (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  recovery_rate decimal NOT NULL,
  base_price_per_pound decimal NOT NULL,
  seasonal_adjustment decimal DEFAULT 1.0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Product forms table
CREATE TABLE product_forms (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id uuid REFERENCES species(id),
  name text NOT NULL,
  processing_yield decimal NOT NULL,
  processing_cost_per_pound decimal NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Shipping rates table
CREATE TABLE shipping_rates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  method text NOT NULL,
  region text NOT NULL,
  base_rate decimal NOT NULL,
  rate_per_pound decimal NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Packaging materials table
CREATE TABLE packaging_materials (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  cost_per_unit decimal NOT NULL,
  weight_capacity decimal NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Price history table
CREATE TABLE price_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id uuid REFERENCES species(id),
  price_per_pound decimal NOT NULL,
  effective_date date NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Quotes table
CREATE TABLE quotes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  customer_name text NOT NULL,
  shipping_address text NOT NULL,
  shipping_method text NOT NULL,
  packaging_id uuid REFERENCES packaging_materials(id),
  subtotal decimal NOT NULL,
  shipping_cost decimal NOT NULL,
  total decimal NOT NULL,
  payment_terms text NOT NULL,
  delivery_timeframe text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Quote items table
CREATE TABLE quote_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  quote_id uuid REFERENCES quotes(id),
  species_id uuid REFERENCES species(id),
  product_form_id uuid REFERENCES product_forms(id),
  input_weight decimal NOT NULL,
  final_weight decimal NOT NULL,
  price_per_pound decimal NOT NULL,
  total_price decimal NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE species ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE packaging_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Public read access for species"
  ON species FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Public read access for product forms"
  ON product_forms FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Public read access for shipping rates"
  ON shipping_rates FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Public read access for packaging materials"
  ON packaging_materials FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Public read access for price history"
  ON price_history FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can manage their own quotes"
  ON quotes FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own quote items"
  ON quote_items FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM quotes
      WHERE quotes.id = quote_items.quote_id
      AND quotes.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM quotes
      WHERE quotes.id = quote_items.quote_id
      AND quotes.user_id = auth.uid()
    )
  );